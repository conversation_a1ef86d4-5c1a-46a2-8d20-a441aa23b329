import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/utils/platform/cross_platform_validator.dart';
import 'package:dasso_reader/dao/book.dart';
import 'package:dasso_reader/dao/book_note.dart';
import 'package:dasso_reader/dao/theme.dart';
import 'package:dasso_reader/enums/reading_info.dart';
import 'package:dasso_reader/enums/text_selection_mode.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/book_style.dart';
import 'package:dasso_reader/models/bookmark.dart';
import 'package:dasso_reader/models/font_model.dart';
import 'package:dasso_reader/models/read_theme.dart';
import 'package:dasso_reader/models/reading_rules.dart';
import 'package:dasso_reader/models/search_result_model.dart';
import 'package:dasso_reader/models/toc_item.dart';
import 'package:dasso_reader/page/book_player/image_viewer.dart';
import 'package:dasso_reader/page/home_page.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/providers/book_list.dart';
import 'package:dasso_reader/providers/bookmark.dart';
import 'package:dasso_reader/service/book_player/book_player_server.dart';
import 'package:dasso_reader/service/dictionary/chinese_segmentation_service.dart';
import 'package:dasso_reader/utils/coordinates_to_part.dart';
import 'package:dasso_reader/utils/js/convert_dart_color_to_js.dart';
import 'package:dasso_reader/models/book_note.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/webView/webview_console_message.dart';
import 'package:dasso_reader/utils/webView/webview_initial_variable.dart';
import 'package:dasso_reader/widgets/bookshelf/book_cover.dart';
import 'package:dasso_reader/widgets/context_menu/unified_context_menu.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/page_turning/diagram.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/page_turning/types_and_icons.dart';
import 'package:dasso_reader/widgets/reading_page/style_widget.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:intl/intl.dart';

class EpubPlayer extends ConsumerStatefulWidget {
  final Book book;
  final String? cfi;
  final Function showOrHideAppBarAndBottomBar;
  final Function onLoadEnd;
  final Function updateParent;

  const EpubPlayer({
    super.key,
    required this.showOrHideAppBarAndBottomBar,
    required this.book,
    this.cfi,
    required this.onLoadEnd,
    required this.updateParent,
  });

  @override
  ConsumerState<EpubPlayer> createState() => EpubPlayerState();
}

class EpubPlayerState extends ConsumerState<EpubPlayer>
    with TickerProviderStateMixin {
  late InAppWebViewController webViewController;
  late ContextMenu contextMenu;
  String cfi = '';
  double percentage = 0.0;
  String chapterTitle = '';
  String chapterHref = '';
  int chapterCurrentPage = 0;
  int chapterTotalPages = 0;
  List<TocItem> toc = [];
  OverlayEntry? contextMenuEntry;
  AnimationController? _animationController;
  Animation<double>? _animation;
  double searchProcess = 0.0;
  List<SearchResultModel> searchResult = [];
  bool showHistory = false;
  bool canGoBack = false;
  bool canGoForward = false;
  late Book book;
  String? backgroundColor;
  String? textColor;
  Timer? styleTimer;

  // Bookmark-related state
  bool bookmarkExists = false;
  String bookmarkCfi = '';

  final StreamController<double> _searchProgressController =
      StreamController<double>.broadcast();

  Stream<double> get searchProgressStream => _searchProgressController.stream;

  final StreamController<List<SearchResultModel>> _searchResultController =
      StreamController<List<SearchResultModel>>.broadcast();

  Stream<List<SearchResultModel>> get searchResultStream =>
      _searchResultController.stream;

  FocusNode focusNode = FocusNode();

  void prevPage() {
    webViewController.evaluateJavascript(source: 'prevPage()');
  }

  void nextPage() {
    webViewController.evaluateJavascript(source: 'nextPage()');
  }

  void prevChapter() {
    webViewController.evaluateJavascript(
      source: '''
      prevSection()
      ''',
    );
  }

  void nextChapter() {
    // Use non-blocking JavaScript execution
    webViewController.evaluateJavascript(
      source: '''
      requestAnimationFrame(() => {
        nextSection();
      });
      ''',
    );
  }

  Future<void> goToPercentage(double value) async {
    // Use non-blocking JavaScript execution with debouncing
    await webViewController.evaluateJavascript(
      source: '''
      if (window.goToPercentDebounce) clearTimeout(window.goToPercentDebounce);
      window.goToPercentDebounce = setTimeout(() => {
        goToPercent($value);
      }, 16); // 60fps debouncing
      ''',
    );
  }

  void changeTheme(ReadTheme readTheme) {
    textColor = readTheme.textColor;
    backgroundColor = readTheme.backgroundColor;

    String bc = convertDartColorToJs(readTheme.backgroundColor);
    String tc = convertDartColorToJs(readTheme.textColor);

    // Use non-blocking theme change with requestAnimationFrame
    webViewController.evaluateJavascript(
      source: '''
      requestAnimationFrame(() => {
        changeStyle({
          backgroundColor: '#$bc',
          fontColor: '#$tc',
        });
      });
      ''',
    );
  }

  void changeStyle(BookStyle bookStyle) {
    styleTimer?.cancel();
    styleTimer = Timer(DesignSystem.durationMedium, () {
      webViewController.evaluateJavascript(
        source: '''
      changeStyle({
        fontSize: ${bookStyle.fontSize},
        spacing: ${bookStyle.lineHeight},
        fontWeight: ${bookStyle.fontWeight},
        paragraphSpacing: ${bookStyle.paragraphSpacing},
        topMargin: ${bookStyle.topMargin},
        bottomMargin: ${bookStyle.bottomMargin},
        sideMargin: ${bookStyle.sideMargin},
        letterSpacing: ${bookStyle.letterSpacing},
        textIndent: ${bookStyle.indent},
        maxColumnCount: ${bookStyle.maxColumnCount},
      })
      ''',
      );
    });
  }

  void changeReadingRules(ReadingRules readingRules) {
    webViewController.evaluateJavascript(
      source: '''
      readingFeatures({
        convertChineseMode: '${readingRules.convertChineseMode.name}',
        bionicReadingMode: ${readingRules.bionicReading},
      })
    ''',
    );
  }

  void changeFont(FontModel font) {
    webViewController.evaluateJavascript(
      source: '''
      changeStyle({
        fontName: '${font.name}',
        fontPath: '${font.path}',
      })
    ''',
    );
  }

  void changePageTurnStyle(PageTurn pageTurnStyle) {
    webViewController.evaluateJavascript(
      source: '''
      changeStyle({
        pageTurnStyle: '${pageTurnStyle.name}',
      })
    ''',
    );
  }

  /// Set text selection mode in JavaScript
  void setTextSelectionMode(TextSelectionMode mode) {
    webViewController.evaluateJavascript(
      source: '''
      setTextSelectionMode('${mode.name}');
    ''',
    );
  }

  void goToHref(String href) =>
      webViewController.evaluateJavascript(source: "goToHref('$href')");

  void goToCfi(String cfi) {
    webViewController.evaluateJavascript(source: "goToCfi('$cfi')");

    // Ensure annotations are visible after navigation
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        AnxLog.info(
          'Re-rendering annotations after CFI navigation to ensure highlights are visible',
        );
        renderAnnotations(webViewController);
      }
    });
  }

  void addAnnotation(BookNote bookNote) {
    webViewController.evaluateJavascript(
      source: '''
      addAnnotation({
        id: ${bookNote.id},
        type: '${bookNote.type}',
        value: '${bookNote.cfi}',
        color: '#${bookNote.color}',
        note: '${bookNote.content.replaceAll('\n', ' ')}',
      })
      ''',
    );
  }

  void removeAnnotation(String cfi) =>
      webViewController.evaluateJavascript(source: "removeAnnotation('$cfi')");

  // Bookmark-related JavaScript methods
  void addBookmarkHere() {
    webViewController.evaluateJavascript(source: 'window.addBookmarkHere()');
  }

  void checkCurrentPageBookmark() {
    webViewController.evaluateJavascript(
      source: 'window.checkCurrentPageBookmark()',
    );
  }

  void showBookmarkIndicator() {
    webViewController.evaluateJavascript(
      source: 'window.showBookmarkIndicator()',
    );
  }

  void hideBookmarkIndicator() {
    webViewController.evaluateJavascript(
      source: 'window.hideBookmarkIndicator()',
    );
  }

  void removeBookmarkHere() {
    webViewController.evaluateJavascript(source: 'window.removeBookmarkHere()');
  }

  void clearSearch() {
    webViewController.evaluateJavascript(source: 'clearSearch()');
    searchResult.clear();
    _searchResultController.add(searchResult);
  }

  void search(String text) {
    clearSearch();
    webViewController.evaluateJavascript(
      source: '''
      search('$text', {
        'scope': 'book',
        'matchCase': false,
        'matchDiacritics': false,
        'matchWholeWords': false,
      })
    ''',
    );
  }

  Future<void> initTts() async =>
      await webViewController.evaluateJavascript(source: 'window.ttsHere()');

  void ttsStop() => webViewController.evaluateJavascript(source: 'ttsStop()');

  Future<String> ttsNext() async {
    final result = await webViewController.callAsyncJavaScript(
      functionBody: 'return await ttsNext()',
    );
    return (result?.value as String?) ?? '';
  }

  Future<String> ttsPrev() async {
    final result = await webViewController.callAsyncJavaScript(
      functionBody: 'return await ttsPrev()',
    );
    return (result?.value as String?) ?? '';
  }

  Future<String> ttsPrevSection() async {
    final result = await webViewController.callAsyncJavaScript(
      functionBody: 'return await ttsPrevSection()',
    );
    return (result?.value as String?) ?? '';
  }

  Future<String> ttsNextSection() async {
    final result = await webViewController.callAsyncJavaScript(
      functionBody: 'return await ttsNextSection()',
    );
    return (result?.value as String?) ?? '';
  }

  Future<String> ttsPrepare() async {
    final result =
        await webViewController.evaluateJavascript(source: 'ttsPrepare()');
    return (result as String?) ?? '';
  }

  Future<bool> isFootNoteOpen() async {
    final result = await webViewController.evaluateJavascript(
      source: 'window.isFootNoteOpen()',
    );
    return (result as bool?) ?? false;
  }

  void backHistory() {
    webViewController.evaluateJavascript(source: 'back()');
  }

  void forwardHistory() {
    webViewController.evaluateJavascript(source: 'forward()');
  }

  Future<String> theChapterContent() async {
    final result = await webViewController.evaluateJavascript(
      source: 'theChapterContent()',
    );
    return (result as String?) ?? '';
  }

  Future<String> previousContent(int count) async {
    final result = await webViewController.evaluateJavascript(
      source: 'previousContent($count)',
    );
    return (result as String?) ?? '';
  }

  /// Preemptively process and cache visible Chinese content
  Future<void> preprocessVisibleContent() async {
    try {
      // Get visible text content from the current view
      final result = await webViewController.evaluateJavascript(
        source: '''
          (function() {
            try {
              // Get text from all visible paragraphs and other text elements
              const visibleElements = Array.from(document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6'))
                .filter(el => {
                  // Filter to elements that are visible and contain text
                  const rect = el.getBoundingClientRect();
                  return rect.top >= 0 &&
                         rect.left >= 0 &&
                         rect.bottom <= window.innerHeight &&
                         rect.right <= window.innerWidth &&
                         el.textContent.trim().length > 0;
                });

              return visibleElements.map(el => el.textContent).join('\\n\\n');
            } catch(e) {
              console.error('Error getting visible content:', e);
              return '';
            }
          })();
        ''',
      );
      final String visibleText = (result as String?) ?? '';

      if (visibleText.isNotEmpty) {
        // Check if the text contains Chinese characters
        if (RegExp(r'[\u4e00-\u9fa5]').hasMatch(visibleText)) {
          AnxLog.info(
            'Preprocessing visible Chinese content (${visibleText.length} chars)',
          );

          // Get book ID for caching
          final bookId = widget.book.id;

          // Process in the background to avoid UI lag with lower priority
          Timer(const Duration(milliseconds: 100), () {
            unawaited(_preprocessChineseText(visibleText, bookId));
          });
        } else {
          AnxLog.info('No Chinese characters found in visible content');
        }
      }
    } catch (e) {
      AnxLog.warning('Error preprocessing visible content: $e');
    }
  }

  /// Process Chinese text in the background
  Future<void> _preprocessChineseText(String text, int bookId) async {
    // Split into manageable chunks to avoid processing huge blocks
    final chunks = _splitIntoChunks(text, 500); // ~500 chars per chunk

    final segmentationService = ChineseSegmentationService();

    // Process each chunk
    for (final chunk in chunks) {
      if (chunk.trim().isEmpty) continue;

      try {
        // This will automatically cache the results with the book ID
        await segmentationService.getWordBoundaries(chunk, bookId: bookId);
      } catch (e) {
        AnxLog.warning('Error preprocessing Chinese text chunk: $e');
      }
    }

    AnxLog.info(
      'Finished preprocessing ${chunks.length} chunks of Chinese text',
    );
  }

  /// Store segmentation data for context menu use
  Future<void> _storeSegmentationDataForContextMenu({
    required String selectedText,
    required String fullNodeText,
    required int startOffset,
    required int endOffset,
    required List<int> selectionRange,
  }) async {
    try {
      final segmentationService = ChineseSegmentationService();
      await segmentationService.initialize();

      // Store comprehensive segmentation data for the selected text and surrounding context
      await segmentationService.storeSegmentationDataForSelection(
        selectedText: selectedText,
        fullNodeText: fullNodeText,
        startOffset: startOffset,
        endOffset: endOffset,
        selectionRange: selectionRange,
        bookId: widget.book.id,
      );

      AnxLog.info('Successfully stored segmentation data for context menu');
    } catch (e) {
      AnxLog.severe('Error storing segmentation data for context menu: $e');
    }
  }

  /// Split text into chunks of specified size
  List<String> _splitIntoChunks(String text, int chunkSize) {
    final List<String> chunks = [];
    for (int i = 0; i < text.length; i += chunkSize) {
      final end = (i + chunkSize < text.length) ? i + chunkSize : text.length;
      chunks.add(text.substring(i, end));
    }
    return chunks;
  }

  void onClick(Map<String, dynamic> location) {
    readingPageKey.currentState?.resetAwakeTimer();
    if (contextMenuEntry != null) {
      removeOverlay();
      return;
    }
    final double x = (location['x'] as num?)?.toDouble() ?? 0.0;
    final double y = (location['y'] as num?)?.toDouble() ?? 0.0;
    final part = coordinatesToPart(x, y);
    final currentPageTurningType = Prefs().pageTurningType;
    final pageTurningType = pageTurningTypes[currentPageTurningType];
    switch (pageTurningType[part]) {
      case PageTurningType.prev:
        prevPage();
        break;
      case PageTurningType.next:
        nextPage();
        break;
      case PageTurningType.menu:
        widget.showOrHideAppBarAndBottomBar(true);
        break;
    }
  }

  Future<void> renderAnnotations(InAppWebViewController controller) async {
    try {
      List<BookNote> annotationList =
          await selectBookNotesByBookId(widget.book.id);

      AnxLog.info(
        'Rendering ${annotationList.length} annotations for book ${widget.book.title}',
      );

      // Filter out invalid annotations and create safe JSON
      List<Map<String, dynamic>> safeAnnotations = [];

      for (BookNote note in annotationList) {
        try {
          // Validate required fields
          if (note.cfi.isNotEmpty &&
              note.type.isNotEmpty &&
              note.color.isNotEmpty) {
            safeAnnotations.add({
              'id': note.id,
              'note': note.content,
              'value': note.cfi,
              'type': note.type,
              'color': '#${note.color}',
            });
          } else {
            AnxLog.warning(
              'Skipping invalid annotation: id=${note.id}, cfi="${note.cfi}", type="${note.type}", color="${note.color}"',
            );
          }
        } catch (e) {
          AnxLog.warning('Error processing annotation ${note.id}: $e');
        }
      }

      AnxLog.info('Filtered to ${safeAnnotations.length} valid annotations');

      String allAnnotations =
          jsonEncode(safeAnnotations).replaceAll('\'', '\\\'');

      // Use the enhanced method that passes data directly to avoid timing issues
      await controller.evaluateJavascript(
        source: '''
        try {
          const annotationsData = $allAnnotations;
          console.log('Flutter: Rendering', annotationsData.length, 'annotations');

          // Set global variable for compatibility
          window.allAnnotations = annotationsData;

          // Use the enhanced method that accepts data directly
          if (window.reader && window.reader.renderAnnotationWithData) {
            window.reader.renderAnnotationWithData(annotationsData);
          } else {
            // Fallback to original method
            window.renderAnnotations();
          }
        } catch (error) {
          console.error('Error rendering annotations:', error);
        }
      ''',
      );

      AnxLog.info('Annotations rendering completed successfully');
    } catch (e) {
      AnxLog.severe('Error in renderAnnotations: $e');
      // Log more details about the error
      try {
        List<BookNote> annotationList =
            await selectBookNotesByBookId(widget.book.id);
        for (int i = 0; i < annotationList.length; i++) {
          BookNote note = annotationList[i];
          AnxLog.info(
            'Annotation $i: id=${note.id}, cfi=${note.cfi}, type=${note.type}, color=${note.color}, content=${note.content}',
          );
        }
      } catch (debugError) {
        AnxLog.severe('Error during debug logging: $debugError');
      }
    }
  }

  Future<void> getThemeColor() async {
    if (Prefs().autoAdjustReadingTheme) {
      List<ReadTheme> themes = await selectThemes();
      final isDayMode =
          Theme.of(navigatorKey.currentContext!).brightness == Brightness.light;
      backgroundColor =
          isDayMode ? themes[0].backgroundColor : themes[1].backgroundColor;
      textColor = isDayMode ? themes[0].textColor : themes[1].textColor;
    } else {
      backgroundColor = Prefs().readTheme.backgroundColor;
      textColor = Prefs().readTheme.textColor;
    }
    setState(() {});
  }

  Future<void> setHandler(InAppWebViewController controller) async {
    String uri = Uri.encodeComponent(widget.book.fileFullPath);
    String url = 'http://127.0.0.1:${Server().port}/book/$uri';
    String initialCfi = widget.cfi ?? widget.book.lastReadPosition;

    await getThemeColor();

    webviewInitialVariable(
      controller,
      url,
      initialCfi,
      backgroundColor: backgroundColor,
      textColor: textColor,
    );

    // Add handler to get word boundary for a specific position
    controller.addJavaScriptHandler(
      handlerName: 'getWordBoundaryForPosition',
      callback: (List<dynamic> args) async {
        try {
          final String text = args[0] as String;
          final int position = args[1] as int;

          // Use dictionary service to get boundary
          final segmentationService = ChineseSegmentationService();
          await segmentationService.initialize(); // Ensure it's initialized

          final boundary = await segmentationService.getWordBoundaryForPosition(
            text,
            position,
          );
          AnxLog.info(
            'Found boundary $boundary for position $position in "${text.substring(0, min(20, text.length))}..."',
          );

          return boundary;
        } catch (e) {
          AnxLog.severe('Error getting word boundary: $e');
          if (args.length >= 2) {
            final int position = args[1] as int;
            return [position, position + 1]; // Default to single character
          }
          return [0, 1]; // Fallback
        }
      },
    );

    // Add handler to store segmentation data for context menu
    controller.addJavaScriptHandler(
      handlerName: 'storeSegmentationData',
      callback: (List<dynamic> args) async {
        try {
          final Map<String, dynamic> data =
              Map<String, dynamic>.from(args[0] as Map);
          final String selectedText = data['selectedText'] as String? ?? '';
          final String fullNodeText = data['fullNodeText'] as String? ?? '';
          final int startOffset = data['startOffset'] as int? ?? 0;
          final int endOffset = data['endOffset'] as int? ?? 0;
          final List<dynamic> selectionRange =
              data['selectionRange'] as List<dynamic>? ?? [0, 0];

          AnxLog.info(
            'Storing segmentation data for selected text: "$selectedText"',
          );

          // Store the segmentation data for later use in context menu
          await _storeSegmentationDataForContextMenu(
            selectedText: selectedText,
            fullNodeText: fullNodeText,
            startOffset: startOffset,
            endOffset: endOffset,
            selectionRange: [
              selectionRange[0] as int,
              selectionRange[1] as int,
            ],
          );

          return true;
        } catch (e) {
          AnxLog.severe('Error storing segmentation data: $e');
          return false;
        }
      },
    );

    // Handler for getting word boundaries for segmentation mode
    controller.addJavaScriptHandler(
      handlerName: 'getWordBoundariesForSelection',
      callback: (List<dynamic> args) async {
        try {
          final Map<String, dynamic> data = args[0] as Map<String, dynamic>;
          final String selectedText = data['selectedText'] as String? ?? '';
          final String fullNodeText = data['fullNodeText'] as String? ?? '';
          final int startOffset = data['startOffset'] as int? ?? 0;
          final int endOffset = data['endOffset'] as int? ?? 0;

          AnxLog.info('Getting word boundaries for selection: "$selectedText"');

          // Use the segmentation service to get word boundaries
          final segmentationService = ChineseSegmentationService();
          await segmentationService.initialize();

          // Get word boundaries for the full node text
          final boundaries =
              await segmentationService.getWordBoundaries(fullNodeText);

          // Find the best word boundary that encompasses the selection
          int adjustedStart = startOffset;
          int adjustedEnd = endOffset;

          for (final boundary in boundaries) {
            final wordStart = boundary[0];
            final wordEnd = boundary[1];

            // Check if this word boundary overlaps with the selection
            if (wordStart <= startOffset && wordEnd >= endOffset) {
              // Selection is entirely within this word
              adjustedStart = wordStart;
              adjustedEnd = wordEnd;
              break;
            } else if (wordStart <= startOffset && wordEnd > startOffset) {
              // Selection starts within this word
              adjustedStart = wordStart;
            } else if (wordStart < endOffset && wordEnd >= endOffset) {
              // Selection ends within this word
              adjustedEnd = wordEnd;
            }
          }

          AnxLog.info('Adjusted boundaries: [$adjustedStart, $adjustedEnd]');

          return {
            'adjustedStart': adjustedStart,
            'adjustedEnd': adjustedEnd,
          };
        } catch (e) {
          AnxLog.severe('Error getting word boundaries: $e');
          return null;
        }
      },
    );

    controller.addJavaScriptHandler(
      handlerName: 'onLoadEnd',
      callback: (args) {
        widget.onLoadEnd();

        // Preemptively process visible Chinese text
        preprocessVisibleContent();

        // Add a delayed annotation rendering to ensure highlights persist after book reopening
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            AnxLog.info(
              'Delayed annotation rendering for highlight persistence',
            );
            renderAnnotations(controller);
          }
        });
      },
    );

    controller.addJavaScriptHandler(
      handlerName: 'onRelocated',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> location = args[0] as Map<String, dynamic>;
        if (cfi == location['cfi']) return;
        setState(() {
          cfi = location['cfi'] as String? ?? '';
          percentage = (location['percentage'] as num?)?.toDouble() ?? 0.0;
          chapterTitle = location['chapterTitle'] as String? ?? '';
          chapterHref = location['chapterHref'] as String? ?? '';
          chapterCurrentPage = (location['chapterCurrentPage'] as int?) ?? 0;
          chapterTotalPages = (location['chapterTotalPages'] as int?) ?? 0;
        });
        saveReadingProgress();
        readingPageKey.currentState?.resetAwakeTimer();

        // Check bookmark status for the new page
        checkCurrentPageBookmark();

        // Preemptively process visible Chinese text when page changes
        preprocessVisibleContent();

        // Ensure annotations remain visible after page navigation
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted) {
            renderAnnotations(controller);
          }
        });
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onClick',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> location = args[0] as Map<String, dynamic>;
        onClick(location);
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onSetToc',
      callback: (List<dynamic> args) {
        final List<dynamic> t = args[0] as List<dynamic>;
        toc =
            t.map((i) => TocItem.fromJson(i as Map<String, dynamic>)).toList();
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onSelectionEnd',
      callback: (List<dynamic> args) {
        removeOverlay();
        final Map<String, dynamic> location = args[0] as Map<String, dynamic>;
        final String cfi = location['cfi'] as String? ?? '';
        final String text = location['text'] as String? ?? '';
        final bool footnote = location['footnote'] as bool? ?? false;
        final Map<String, dynamic> pos =
            location['pos'] as Map<String, dynamic>? ?? {};
        final Map<String, dynamic> point =
            pos['point'] as Map<String, dynamic>? ?? {};
        final double x = (point['x'] as num?)?.toDouble() ?? 0.0;
        final double y = (point['y'] as num?)?.toDouble() ?? 0.0;
        final String dir = pos['dir'] as String? ?? '';
        showUnifiedContextMenu(context, x, y, dir, text, cfi, null, footnote);
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onAnnotationClick',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> annotation = args[0] as Map<String, dynamic>;
        final Map<String, dynamic> annotationData =
            annotation['annotation'] as Map<String, dynamic>? ?? {};
        final int id = annotationData['id'] as int? ?? 0;
        final String cfi = annotationData['value'] as String? ?? '';
        final String note = annotationData['note'] as String? ?? '';
        final Map<String, dynamic> pos =
            annotation['pos'] as Map<String, dynamic>? ?? {};
        final Map<String, dynamic> point =
            pos['point'] as Map<String, dynamic>? ?? {};
        final double x = (point['x'] as num?)?.toDouble() ?? 0.0;
        final double y = (point['y'] as num?)?.toDouble() ?? 0.0;
        final String dir = pos['dir'] as String? ?? '';
        showUnifiedContextMenu(context, x, y, dir, note, cfi, id, false);
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onSearch',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> search = args[0] as Map<String, dynamic>;
        setState(() {
          if (search['process'] != null) {
            searchProcess = (search['process'] as num?)?.toDouble() ?? 0.0;
            _searchProgressController.add(searchProcess);
          } else {
            searchResult.add(SearchResultModel.fromJson(search));
            _searchResultController.add(searchResult);
          }
        });
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'renderAnnotations',
      callback: (args) {
        renderAnnotations(controller);
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onPushState',
      callback: (List<dynamic> args) {
        final Map<String, dynamic> state = args[0] as Map<String, dynamic>;
        canGoBack = state['canGoBack'] as bool? ?? false;
        canGoForward = state['canGoForward'] as bool? ?? false;
        if (!mounted) return;
        setState(() {
          showHistory = true;
        });
        Future.delayed(const Duration(seconds: 20), () {
          if (!mounted) return;
          setState(() {
            showHistory = false;
          });
        });
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onImageClick',
      callback: (List<dynamic> args) {
        final String image = args[0] as String? ?? '';
        AdaptiveNavigation.push(
          context,
          ImageViewer(
            image: image,
            bookName: widget.book.title,
          ),
        );
      },
    );
    controller.addJavaScriptHandler(
      handlerName: 'onFootnoteClose',
      callback: (List<dynamic> args) {
        removeOverlay();
      },
    );

    // Bookmark-related handlers
    controller.addJavaScriptHandler(
      handlerName: 'handleBookmark',
      callback: (List<dynamic> args) async {
        try {
          final Map<String, dynamic> bookmarkData =
              args[0] as Map<String, dynamic>;
          final String content = bookmarkData['content'] as String? ?? '';
          final String cfi = bookmarkData['cfi'] as String? ?? '';
          final String chapter = bookmarkData['chapter'] as String? ?? '';
          final double percentage =
              (bookmarkData['percentage'] as num?)?.toDouble() ?? 0.0;

          final bookmark = BookmarkModel(
            bookId: book.id,
            content: content,
            cfi: cfi,
            chapter: chapter,
            percentage: percentage,
            updateTime: DateTime.now(),
          );

          await ref
              .read(BookmarkProvider(book.id).notifier)
              .addBookmark(bookmark);

          // Update local state immediately
          setState(() {
            bookmarkExists = true;
            bookmarkCfi = cfi;
          });

          // Update reading page state
          widget.updateParent();

          AnxLog.info('Bookmark added: $content');
        } catch (e) {
          AnxLog.severe('Error handling bookmark: $e');
        }
      },
    );

    controller.addJavaScriptHandler(
      handlerName: 'checkBookmark',
      callback: (List<dynamic> args) async {
        try {
          final String cfi = args[0] as String? ?? '';
          final bookmarks =
              ref.read(BookmarkProvider(book.id)).valueOrNull ?? [];
          final exists = bookmarks.any((bookmark) => bookmark.cfi == cfi);

          setState(() {
            bookmarkExists = exists;
            bookmarkCfi = cfi;
          });

          // Show or hide visual indicator based on bookmark existence
          if (exists) {
            showBookmarkIndicator();
          } else {
            hideBookmarkIndicator();
          }

          // Update reading page state
          widget.updateParent();

          return exists;
        } catch (e) {
          AnxLog.severe('Error checking bookmark: $e');
          return false;
        }
      },
    );

    controller.addJavaScriptHandler(
      handlerName: 'removeBookmark',
      callback: (List<dynamic> args) async {
        try {
          final Map<String, dynamic> bookmarkData =
              args[0] as Map<String, dynamic>;
          final String cfi = bookmarkData['cfi'] as String? ?? '';

          // Remove bookmark from database
          ref.read(BookmarkProvider(book.id).notifier).removeBookmark(cfi: cfi);

          // Update local state immediately
          setState(() {
            bookmarkExists = false;
            bookmarkCfi = '';
          });

          // Update reading page state
          widget.updateParent();

          AnxLog.info('Bookmark removed: $cfi');
        } catch (e) {
          AnxLog.severe('Error removing bookmark: $e');
        }
      },
    );
  }

  Future<void> onWebViewCreated(InAppWebViewController controller) async {
    // Validate WebView support for cross-platform compatibility
    if (!CrossPlatformValidator.isWebViewSupported()) {
      AnxLog.severe('WebView not supported on current platform');
      return;
    }

    // Validate WebView configuration
    CrossPlatformValidator.validateWebViewConfiguration();

    // Android-specific debug setting
    if (defaultTargetPlatform == TargetPlatform.android) {
      await InAppWebViewController.setWebContentsDebuggingEnabled(true);
    }
    webViewController = controller;
    setHandler(controller);
  }

  void removeOverlay() {
    if (contextMenuEntry == null || contextMenuEntry?.mounted == false) return;
    contextMenuEntry?.remove();
    contextMenuEntry = null;
  }

  void _handleKeyAndMouseEvents(KeyEvent event) {
    final nextPageEvent = [
      LogicalKeyboardKey.arrowRight,
      LogicalKeyboardKey.arrowDown,
      LogicalKeyboardKey.pageDown,
      LogicalKeyboardKey.space,
    ];

    final prevPageEvent = [
      LogicalKeyboardKey.arrowLeft,
      LogicalKeyboardKey.arrowUp,
      LogicalKeyboardKey.pageUp,
    ];

    final appBarEvent = [
      LogicalKeyboardKey.enter,
    ];

    if (event is KeyDownEvent) {
      if (nextPageEvent.contains(event.logicalKey)) {
        nextPage();
      } else if (prevPageEvent.contains(event.logicalKey)) {
        prevPage();
      } else if (appBarEvent.contains(event.logicalKey)) {
        widget.showOrHideAppBarAndBottomBar(true);
      }
    }
  }

  Future<void> _handlePointerEvents(PointerEvent event) async {
    if (await isFootNoteOpen() || Prefs().pageTurnStyle == PageTurn.scroll) {
      return;
    }
    if (event is PointerScrollEvent) {
      if (event.scrollDelta.dy > 0) {
        nextPage();
      } else {
        prevPage();
      }
    }
  }

  @override
  void initState() {
    book = widget.book;
    focusNode.requestFocus();

    contextMenu = ContextMenu(
      settings: ContextMenuSettings(hideDefaultSystemContextMenuItems: true),
      onCreateContextMenu: (hitTestResult) async {
        // webViewController.evaluateJavascript(source: "showContextMenu()");
      },
      onHideContextMenu: () {
        // removeOverlay();
      },
    );
    if (Prefs().openBookAnimation) {
      _animationController = AnimationController(
        duration: DesignSystem.durationSlow + const Duration(milliseconds: 100),
        vsync: this,
      );
      _animation =
          Tween<double>(begin: 1.0, end: 0.0).animate(_animationController!);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _animationController!.forward();
      });
    }
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  Future<void> saveReadingProgress() async {
    if (cfi == '') return;
    Book book = widget.book;
    book.lastReadPosition = cfi;
    book.readingPercentage = percentage;
    await updateBook(book);
    if (mounted) {
      ref.read(bookListProvider.notifier).refresh();
    }
  }

  @override
  void dispose() {
    // Cancel timers to prevent memory leaks
    styleTimer?.cancel();

    // Dispose of animation controller
    _animationController?.dispose();

    // Dispose of stream controllers to prevent memory leaks
    _searchProgressController.close();
    _searchResultController.close();

    // Dispose of focus node
    focusNode.dispose();

    // Clear web view cache on mobile platforms
    if (defaultTargetPlatform == TargetPlatform.android ||
        defaultTargetPlatform == TargetPlatform.iOS ||
        defaultTargetPlatform == TargetPlatform.macOS) {
      InAppWebViewController.clearAllCache();
    }

    // Save progress and clean up overlay
    saveReadingProgress();
    removeOverlay();

    super.dispose();
  }

  String indexHtmlPath =
      'http://127.0.0.1:${Server().port}/foliate-js/index.html';

  InAppWebViewSettings initialSettings = InAppWebViewSettings(
    supportZoom: false,
    transparentBackground: true,
    isInspectable: kDebugMode,
  );

  void changeReadingInfo() {
    setState(() {});
  }

  Widget readingInfoWidget() {
    if (chapterCurrentPage == 0) {
      return const SizedBox();
    }

    // Use WCAG AAA compliant colors for reading info overlay
    TextStyle textStyle = TextStyle(
      color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
      fontSize: DesignSystem.getAdjustedFontSize(
        context,
        DesignSystem.fontSizeXS,
      ),
    );

    Widget chapterTitleWidget = Text(
      (chapterCurrentPage == 1 ? widget.book.title : chapterTitle),
      style: textStyle,
    );

    Widget chapterProgressWidget = Text(
      '$chapterCurrentPage/$chapterTotalPages',
      style: textStyle,
    );

    Widget bookProgressWidget =
        Text('${(percentage * 100).toStringAsFixed(2)}%', style: textStyle);

    Widget timeWidget() => StreamBuilder<void>(
          stream: Stream<void>.periodic(const Duration(seconds: 1)),
          builder: (context, snapshot) {
            String currentTime = DateFormat('HH:mm').format(DateTime.now());
            return Text(currentTime, style: textStyle);
          },
        );

    Widget batteryWidget = FutureBuilder(
      future: Battery().batteryLevel,
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Stack(
            alignment: Alignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(
                  0,
                  DesignSystem.spaceXS /
                      5, // 0.8 (fine-tuned for battery alignment)
                  DesignSystem.spaceXS / 2, // 2.0
                  0,
                ),
                child: Text(
                  '${snapshot.data}',
                  style: TextStyle(
                    // Use light gray color for battery percentage text to contrast against black battery
                    color: Colors.grey[300],
                    fontSize: DesignSystem.getAdjustedFontSize(
                      context,
                      DesignSystem.fontSizeXS,
                    ),
                  ),
                ),
              ),
              // Rotate the battery icon to make it horizontal like the original design
              Transform.rotate(
                angle:
                    1.5708, // 90 degrees in radians to make battery horizontal
                child: Icon(
                  AdaptiveIcons.battery,
                  size: DesignSystem.getAdjustedIconSize(27),
                  color: DesignSystem.getSettingsTextColor(
                    context,
                    isPrimary: true,
                  ),
                ),
              ),
            ],
          );
        } else {
          return const SizedBox();
        }
      },
    );

    Widget batteryAndTimeWidget() => Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            batteryWidget,
            const SizedBox(width: DesignSystem.spaceXS + 1), // 5.0
            timeWidget(),
          ],
        );

    Widget getWidget(ReadingInfoEnum readingInfoEnum) {
      switch (readingInfoEnum) {
        case ReadingInfoEnum.chapterTitle:
          return chapterTitleWidget;
        case ReadingInfoEnum.chapterProgress:
          return chapterProgressWidget;
        case ReadingInfoEnum.bookProgress:
          return bookProgressWidget;
        case ReadingInfoEnum.battery:
          return batteryWidget;
        case ReadingInfoEnum.time:
          return timeWidget();
        case ReadingInfoEnum.batteryAndTime:
          return batteryAndTimeWidget();
        case ReadingInfoEnum.none:
          return const SizedBox();
      }
    }

    List<Widget> headerWidgets = [
      getWidget(Prefs().readingInfo.headerLeft),
      getWidget(Prefs().readingInfo.headerCenter),
      getWidget(Prefs().readingInfo.headerRight),
    ];

    List<Widget> footerWidgets = [
      getWidget(Prefs().readingInfo.footerLeft),
      getWidget(Prefs().readingInfo.footerCenter),
      getWidget(Prefs().readingInfo.footerRight),
    ];

    return Container(
      padding: const EdgeInsets.fromLTRB(
        DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
        DesignSystem.spaceS + DesignSystem.spaceXS, // 10.0 (8 + 2)
        DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
        DesignSystem.spaceXL - DesignSystem.spaceXS, // 30.0 (32 - 2)
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SafeArea(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: headerWidgets,
            ),
          ),
          const Spacer(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: footerWidgets,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: focusNode,
      onKeyEvent: _handleKeyAndMouseEvents,
      child: Listener(
        onPointerSignal: (event) {
          _handlePointerEvents(event);
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              SizedBox.expand(
                child: InAppWebView(
                  webViewEnvironment: webViewEnvironment,
                  initialUrlRequest: URLRequest(url: WebUri(indexHtmlPath)),
                  initialSettings: initialSettings,
                  contextMenu: contextMenu,
                  // onLoadStop: (controller, url) => onWebViewCreated(controller),
                  onConsoleMessage: (controller, consoleMessage) {
                    if (consoleMessage.message.contains('loadBook')) {
                      onWebViewCreated(controller);
                    }
                    webviewConsoleMessage(controller, consoleMessage);
                  },
                ),
              ),
              readingInfoWidget(),
              if (showHistory)
                Positioned(
                  bottom: 30,
                  left: 0,
                  child: Container(
                    width: ResponsiveSystem.getScreenWidth(context),
                    padding: const EdgeInsets.all(
                      DesignSystem.spaceS +
                          DesignSystem.spaceXS, // 10.0 (8 + 2)
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (canGoBack)
                          SemanticHelpers.button(
                            context: context,
                            label: 'Previous page',
                            hint:
                                'Navigate back to previous page in reading history',
                            onTap: () {
                              backHistory();
                            },
                            child: IconButton(
                              onPressed: () {
                                backHistory();
                              },
                              icon: Icon(AdaptiveIcons.arrowBackIos),
                            ),
                          ),
                        if (canGoForward)
                          SemanticHelpers.button(
                            context: context,
                            label: 'Next page',
                            hint:
                                'Navigate forward to next page in reading history',
                            onTap: () {
                              forwardHistory();
                            },
                            child: IconButton(
                              onPressed: () {
                                forwardHistory();
                              },
                              icon: Icon(AdaptiveIcons.arrowForwardIos),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              if (Prefs().openBookAnimation)
                SizedBox.expand(
                  child: Prefs().openBookAnimation
                      ? IgnorePointer(
                          ignoring: true,
                          child: FadeTransition(
                            opacity: _animation!,
                            child: bookCover(context, widget.book),
                          ),
                        )
                      : bookCover(context, widget.book),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
