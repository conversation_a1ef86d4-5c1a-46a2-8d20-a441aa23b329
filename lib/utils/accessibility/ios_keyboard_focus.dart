import 'package:flutter/material.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';

/// iOS keyboard focus management utilities for DassoShu Reader
/// 
/// Provides consistent, reusable solutions for iOS keyboard focus issues
/// following established cross-platform development patterns.
class IOSKeyboardFocus {
  // Private constructor to prevent instantiation
  IOSKeyboardFocus._();

  /// Applies iOS-compatible focus handling to a TextField or TextFormField
  /// 
  /// This method wraps the text field with proper iOS keyboard focus behavior:
  /// - Adds explicit tap handling for iOS keyboard activation
  /// - Configures proper focus listeners for iOS timing
  /// - Sets appropriate keyboard and input settings for Chinese text
  /// 
  /// Usage:
  /// ```dart
  /// IOSKeyboardFocus.wrapTextField(
  ///   child: TextField(
  ///     controller: controller,
  ///     focusNode: focusNode,
  ///     // ... other properties
  ///   ),
  ///   focusNode: focusNode,
  /// )
  /// ```
  static Widget wrapTextField({
    required Widget child,
    required FocusNode focusNode,
    bool enableChineseInput = true,
  }) {
    return GestureDetector(
      // Add explicit tap handling for iOS keyboard focus
      onTap: () {
        // Request focus explicitly for iOS compatibility
        focusNode.requestFocus();
      },
      child: child,
    );
  }

  /// Configures a FocusNode with iOS-compatible focus handling
  /// 
  /// Call this in initState() to set up proper iOS keyboard behavior:
  /// ```dart
  /// @override
  /// void initState() {
  ///   super.initState();
  ///   IOSKeyboardFocus.setupFocusNode(_focusNode);
  /// }
  /// ```
  static void setupFocusNode(FocusNode focusNode) {
    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        // Ensure keyboard appears on iOS when focus is gained
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Force keyboard to show on iOS
          if (focusNode.context != null && focusNode.context!.mounted) {
            FocusScope.of(focusNode.context!).requestFocus(focusNode);
          }
        });
      }
    });
  }

  /// Returns iOS-compatible TextField properties for Chinese input
  /// 
  /// Provides consistent keyboard and input settings across all text fields:
  /// ```dart
  /// TextField(
  ///   ...IOSKeyboardFocus.getTextFieldProperties(),
  ///   // ... other properties
  /// )
  /// ```
  static Map<String, dynamic> getTextFieldProperties({
    bool enableChineseInput = true,
    TextInputAction? textInputAction,
    bool multiline = false,
  }) {
    return {
      'keyboardType': multiline ? TextInputType.multiline : TextInputType.text,
      'textCapitalization': enableChineseInput 
          ? TextCapitalization.none 
          : TextCapitalization.sentences,
      'textInputAction': textInputAction ?? 
          (multiline ? TextInputAction.newline : TextInputAction.done),
      'autofocus': false, // Prevent unwanted auto-focus on iOS
    };
  }

  /// Adds iOS-compatible onTap handler to TextField properties
  /// 
  /// Use this to add proper tap handling to existing TextField widgets:
  /// ```dart
  /// TextField(
  ///   onTap: IOSKeyboardFocus.createOnTapHandler(focusNode),
  ///   // ... other properties
  /// )
  /// ```
  static VoidCallback createOnTapHandler(FocusNode focusNode) {
    return () {
      // Ensure focus is requested when tapped
      if (!focusNode.hasFocus) {
        focusNode.requestFocus();
      }
    };
  }

  /// Creates a complete iOS-compatible TextField with all necessary fixes
  /// 
  /// This is a convenience method that creates a fully configured TextField
  /// with all iOS keyboard fixes applied:
  /// ```dart
  /// IOSKeyboardFocus.createTextField(
  ///   controller: controller,
  ///   focusNode: focusNode,
  ///   decoration: decoration,
  ///   onSubmitted: onSubmitted,
  /// )
  /// ```
  static Widget createTextField({
    required TextEditingController controller,
    required FocusNode focusNode,
    InputDecoration? decoration,
    TextStyle? style,
    ValueChanged<String>? onSubmitted,
    ValueChanged<String>? onChanged,
    bool enableChineseInput = true,
    bool multiline = false,
    int? maxLines,
    bool expands = false,
    TextInputAction? textInputAction,
  }) {
    final properties = getTextFieldProperties(
      enableChineseInput: enableChineseInput,
      textInputAction: textInputAction,
      multiline: multiline,
    );

    return wrapTextField(
      focusNode: focusNode,
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        decoration: decoration,
        style: style,
        onSubmitted: onSubmitted,
        onChanged: onChanged,
        onTap: createOnTapHandler(focusNode),
        keyboardType: properties['keyboardType'] as TextInputType,
        textCapitalization: properties['textCapitalization'] as TextCapitalization,
        textInputAction: properties['textInputAction'] as TextInputAction,
        autofocus: properties['autofocus'] as bool,
        maxLines: maxLines,
        expands: expands,
      ),
    );
  }

  /// Creates a complete iOS-compatible TextFormField with all necessary fixes
  /// 
  /// Similar to createTextField but for TextFormField widgets:
  /// ```dart
  /// IOSKeyboardFocus.createTextFormField(
  ///   controller: controller,
  ///   focusNode: focusNode,
  ///   decoration: decoration,
  ///   validator: validator,
  /// )
  /// ```
  static Widget createTextFormField({
    required FocusNode focusNode,
    TextEditingController? controller,
    String? initialValue,
    InputDecoration? decoration,
    TextStyle? style,
    ValueChanged<String>? onChanged,
    FormFieldValidator<String>? validator,
    bool enableChineseInput = true,
    bool multiline = false,
    int? maxLines,
    int? minLines,
    TextInputAction? textInputAction,
    bool enabled = true,
  }) {
    final properties = getTextFieldProperties(
      enableChineseInput: enableChineseInput,
      textInputAction: textInputAction,
      multiline: multiline,
    );

    return wrapTextField(
      focusNode: focusNode,
      child: TextFormField(
        controller: controller,
        initialValue: initialValue,
        focusNode: focusNode,
        decoration: decoration,
        style: style,
        onChanged: onChanged,
        validator: validator,
        enabled: enabled,
        onTap: enabled ? createOnTapHandler(focusNode) : null,
        keyboardType: properties['keyboardType'] as TextInputType,
        textCapitalization: properties['textCapitalization'] as TextCapitalization,
        textInputAction: properties['textInputAction'] as TextInputAction,
        autofocus: properties['autofocus'] as bool,
        maxLines: maxLines,
        minLines: minLines,
      ),
    );
  }

  /// Programmatically focuses a text field with iOS-compatible behavior
  /// 
  /// Use this method to focus text fields programmatically:
  /// ```dart
  /// IOSKeyboardFocus.focusTextField(context, focusNode);
  /// ```
  static void focusTextField(BuildContext context, FocusNode focusNode) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        focusNode.requestFocus();
        // Additional iOS keyboard trigger
        FocusScope.of(context).requestFocus(focusNode);
      }
    });
  }
}
